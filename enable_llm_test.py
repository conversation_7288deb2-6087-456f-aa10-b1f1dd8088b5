#!/usr/bin/env python3
"""
Test to enable LLM and ensure output quality is maintained
"""

import asyncio
import os
from Document_Validation_Agentic import DocumentValidationAgent, test_openai_connection

async def test_llm_enablement():
    """Test enabling LLM and compare with offline results"""
    
    print("🔍 TESTING LLM ENABLEMENT")
    print("=" * 80)
    
    # Check current API key status
    api_key = os.getenv('OPENAI_API_KEY')
    print(f"📋 Current API Key Status: {'SET' if api_key else 'NOT SET'}")
    
    if not api_key:
        print("\n🔑 OPENAI API KEY SETUP REQUIRED")
        print("=" * 60)
        print("To enable LLM functionality, you need to:")
        print("1. Get an OpenAI API key from https://platform.openai.com/api-keys")
        print("2. Set it as an environment variable:")
        print("   - Windows: set OPENAI_API_KEY=your_key_here")
        print("   - Linux/Mac: export OPENAI_API_KEY=your_key_here")
        print("3. Or create a .env file with: OPENAI_API_KEY=your_key_here")
        print("\n⚠️ For now, testing with offline mode...")
        
        # Test offline mode first
        print("\n🧪 TESTING OFFLINE MODE (CURRENT):")
        print("=" * 60)
        
        agent_offline = DocumentValidationAgent(offline_mode=True)
        
        # Auto-detect files
        sample_files = []
        generated_files = []
        
        if os.path.exists("sample"):
            sample_files = [f for f in os.listdir("sample") if f.endswith(".pdf")]
        if os.path.exists("generated"):
            generated_files = [f for f in os.listdir("generated") if f.endswith(".pdf")]
        
        if sample_files and generated_files:
            sample_pdf = f"sample/{sample_files[0]}"
            generated_pdf = f"generated/{generated_files[0]}"
            
            print(f"📄 Testing with: {sample_files[0]}")
            
            result_offline = await agent_offline.validate_documents(sample_pdf, generated_pdf)
            
            print(f"\n📊 OFFLINE MODE RESULTS:")
            print(f"   Overall Score: {result_offline.overall_score}/100")
            print(f"   Static Text Score: {result_offline.static_text_score}/100")
            print(f"   Dynamic Field Score: {result_offline.dynamic_field_score}/100")
            print(f"   Populated Fields: {len(result_offline.populated_dynamic_fields)}")
            print(f"   Blank Fields: {len(result_offline.blank_dynamic_fields)}")
            print(f"   Processing Time: {result_offline.processing_time:.2f}s")
            
            return result_offline
        else:
            print("❌ No PDF files found for testing")
            return None
    
    else:
        print(f"✅ API Key found (length: {len(api_key)} characters)")
        
        # Test OpenAI connection
        print("\n🌐 TESTING OPENAI CONNECTION:")
        print("=" * 60)
        
        try:
            connection_ok = await test_openai_connection("gpt-4o")
            
            if connection_ok:
                print("✅ OpenAI connection successful!")
                
                # Test both modes and compare
                print("\n🔄 COMPARING OFFLINE vs LLM MODES:")
                print("=" * 60)
                
                # Auto-detect files
                sample_files = []
                generated_files = []
                
                if os.path.exists("sample"):
                    sample_files = [f for f in os.listdir("sample") if f.endswith(".pdf")]
                if os.path.exists("generated"):
                    generated_files = [f for f in os.listdir("generated") if f.endswith(".pdf")]
                
                if sample_files and generated_files:
                    sample_pdf = f"sample/{sample_files[0]}"
                    generated_pdf = f"generated/{generated_files[0]}"
                    
                    print(f"📄 Testing with: {sample_files[0]}")
                    
                    # Test offline mode
                    print("\n1️⃣ OFFLINE MODE TEST:")
                    agent_offline = DocumentValidationAgent(offline_mode=True)
                    result_offline = await agent_offline.validate_documents(sample_pdf, generated_pdf)
                    
                    # Test LLM mode
                    print("\n2️⃣ LLM MODE TEST:")
                    agent_llm = DocumentValidationAgent(offline_mode=False)
                    result_llm = await agent_llm.validate_documents(sample_pdf, generated_pdf)
                    
                    # Compare results
                    print(f"\n📊 COMPARISON RESULTS:")
                    print("=" * 60)
                    print(f"{'Metric':<25} {'Offline':<15} {'LLM':<15} {'Difference':<15}")
                    print("-" * 70)
                    print(f"{'Overall Score':<25} {result_offline.overall_score:<15} {result_llm.overall_score:<15} {result_llm.overall_score - result_offline.overall_score:<15}")
                    print(f"{'Static Text Score':<25} {result_offline.static_text_score:<15} {result_llm.static_text_score:<15} {result_llm.static_text_score - result_offline.static_text_score:<15}")
                    print(f"{'Dynamic Field Score':<25} {result_offline.dynamic_field_score:<15} {result_llm.dynamic_field_score:<15} {result_llm.dynamic_field_score - result_offline.dynamic_field_score:<15}")
                    print(f"{'Populated Fields':<25} {len(result_offline.populated_dynamic_fields):<15} {len(result_llm.populated_dynamic_fields):<15} {len(result_llm.populated_dynamic_fields) - len(result_offline.populated_dynamic_fields):<15}")
                    print(f"{'Blank Fields':<25} {len(result_offline.blank_dynamic_fields):<15} {len(result_llm.blank_dynamic_fields):<15} {len(result_llm.blank_dynamic_fields) - len(result_offline.blank_dynamic_fields):<15}")
                    print(f"{'Processing Time':<25} {result_offline.processing_time:.2f}s{'':<8} {result_llm.processing_time:.2f}s{'':<8} {result_llm.processing_time - result_offline.processing_time:.2f}s{'':<8}")
                    
                    # Quality assessment
                    print(f"\n🎯 QUALITY ASSESSMENT:")
                    print("=" * 60)
                    
                    if result_llm.overall_score >= result_offline.overall_score:
                        print("✅ LLM mode maintains or improves overall quality")
                    else:
                        print("⚠️ LLM mode shows lower overall score")
                    
                    if len(result_llm.populated_dynamic_fields) >= len(result_offline.populated_dynamic_fields):
                        print("✅ LLM mode maintains or improves field detection")
                    else:
                        print("⚠️ LLM mode detects fewer fields")
                    
                    if abs(len(result_llm.blank_dynamic_fields) - len(result_offline.blank_dynamic_fields)) <= 2:
                        print("✅ LLM mode shows consistent blank field detection")
                    else:
                        print("⚠️ LLM mode shows significant difference in blank field detection")
                    
                    return result_llm
                else:
                    print("❌ No PDF files found for testing")
                    return None
            else:
                print("❌ OpenAI connection failed")
                return None
                
        except Exception as e:
            print(f"❌ Error testing OpenAI connection: {e}")
            return None

async def setup_llm_environment():
    """Help user set up LLM environment"""
    
    print("\n🔧 LLM ENVIRONMENT SETUP GUIDE")
    print("=" * 80)
    
    # Check if .env file exists
    if not os.path.exists(".env"):
        print("📝 Creating .env file template...")
        
        env_template = """# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Model configuration
OPENAI_MODEL=gpt-4o
"""
        
        with open(".env", "w") as f:
            f.write(env_template)
        
        print("✅ Created .env file template")
        print("📝 Please edit .env file and add your OpenAI API key")
    else:
        print("✅ .env file already exists")
    
    print("\n🔑 TO ENABLE LLM:")
    print("1. Get OpenAI API key: https://platform.openai.com/api-keys")
    print("2. Edit .env file and replace 'your_openai_api_key_here' with your actual key")
    print("3. Restart the program")
    print("\n💡 The system will automatically detect the API key and enable LLM mode")

if __name__ == "__main__":
    result = asyncio.run(test_llm_enablement())
    
    if not os.getenv('OPENAI_API_KEY'):
        asyncio.run(setup_llm_environment())
    
    print(f"\n🎯 SUMMARY:")
    print("=" * 80)
    if result:
        print("✅ System is working correctly")
        print("💡 To enable LLM: Set OPENAI_API_KEY environment variable")
        print("🚀 LLM will provide enhanced analysis while maintaining quality")
    else:
        print("⚠️ Please set up OpenAI API key to enable LLM functionality")
        print("📖 Follow the setup guide above")
