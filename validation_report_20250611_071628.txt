🚀 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-06-11T07:16:28.096535
Processing Time: 2.91 seconds
Overall Score: 61/100
Validation Status: FAIL

📊 DETAILED SCORES:
   Static Text Compliance: 70/100
   Dynamic Field Completeness: 50/100

🔍 STATIC TEXT ANALYSIS:
   Differences Found: 5
   1. DIFFERENT_TEXT:
      Location: Line 341
      Severity: LOW
      Sample: "COMMERCIAL AUTO COVERAGE FORM"
      Generated: "MOTOR CARRIER COVERAGE FORM"
      Impact: Minor text variation

   2. DIFFERENT_TEXT:
      Location: Targeted detection (Handling Of Property)
      Severity: HIGH
      Sample: "Handling Of Property"
      Generated: "7. Man Handling Of Property"
      Impact: Important content difference that may affect document accuracy

   3. DIFFERENT_TEXT:
      Location: Targeted detection (Handling Of Property)
      Severity: HIGH
      Sample: "Handling Of Property"
      Generated: "from the handling of property:"
      Impact: Important content difference that may affect document accuracy

   4. DIFFERENT_TEXT:
      Location: Targeted detection (Handling Of Property)
      Severity: HIGH
      Sample: "from the handling of property:"
      Generated: "7. Man Handling Of Property"
      Impact: Important content difference that may affect document accuracy

   5. DIFFERENT_TEXT:
      Location: Line 289
      Severity: MEDIUM
      Sample: "b. After it is moved from the covered "auto" to the"
      Generated: "place where it is finally delivered by the "insured"."
      Impact: Significant text difference that should be reviewed

✅ POPULATED DYNAMIC FIELDS ANALYSIS:
   Total Populated Fields: 4

   1. DATE:
      Type: date
      Value: 28/05/2025
      Location: Page 6
      Page Number: 6

   2. COMPANY NAME:
      Type: company_name
      Value: refer to the company
      Location: Page 2
      Page Number: 2

   3. ORGANIZATION NAME:
      Type: organization_name
      Value: This inc
      Location: Page 2
      Page Number: 2

   4. COVERAGE:
      Type: coverage
      Value: 1.
      Location: Page 3
      Page Number: 3

🚨 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 4
   Critical Issues: 0

   1. READ THE:
      Type: read_the
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Read the field is blank - contains visual gap indicating missing content
      Recommendation: Populate Read the with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   2. DUTIES AND WHAT:
      Type: duties_and_what
      Status: BLANK
      Business Impact: MEDIUM
      Impact: duties and what field is blank - contains visual gap indicating missing content
      Recommendation: Populate duties and what with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   3. REFER TO:
      Type: refer_to
      Status: BLANK
      Business Impact: MEDIUM
      Impact: refer to field is blank - contains visual gap indicating missing content
      Recommendation: Populate refer to with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   4. THE WORDS:
      Type: the_words
      Status: BLANK
      Business Impact: MEDIUM
      Impact: The words field is blank - contains visual gap indicating missing content
      Recommendation: Populate The words with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

💡 RECOMMENDATIONS:
   1. Fix 5 static text differences

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Validate regulatory compliance
4. Re-run validation after fixes
============================================================