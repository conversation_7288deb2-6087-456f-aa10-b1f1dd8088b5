#!/usr/bin/env python3
"""
Debug why "Mobile Equipment Subject" is being detected as a blank field
"""

import asyncio
import re
from Document_Validation_Agentic import DocumentValidationAgent

async def debug_mobile_equipment():
    """Debug the Mobile Equipment Subject detection"""
    
    print("🔍 DEBUGGING MOBILE EQUIPMENT SUBJECT DETECTION")
    print("=" * 80)
    
    # Initialize the validation agent in offline mode
    agent = DocumentValidationAgent(offline_mode=True)
    
    # Test files
    generated_pdf = "generated/policy_1367076882_generated.pdf"
    
    print(f"📄 Generated PDF: {generated_pdf}")
    print("=" * 80)
    
    try:
        # Extract content first
        content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
        lines = content.split('\n')
        
        print("🔍 SEARCHING FOR 'MOBILE EQUIPMENT' IN CONTENT:")
        print("=" * 60)
        
        mobile_lines = []
        for line_num, line in enumerate(lines, 1):
            if "mobile equipment" in line.lower():
                # Find page
                page_num = "Unknown"
                for i in range(line_num - 1, -1, -1):
                    if "--- Page" in lines[i]:
                        page_num = lines[i].split("Page")[1].split("---")[0].strip()
                        break
                
                mobile_lines.append({
                    'line_num': line_num,
                    'page': page_num,
                    'content': line.strip()
                })
        
        print(f"Found {len(mobile_lines)} lines containing 'mobile equipment':")
        
        for i, line_info in enumerate(mobile_lines, 1):
            print(f"{i}. Page {line_info['page']}, Line {line_info['line_num']}:")
            print(f"   Content: {line_info['content']}")
            
            # Check if this line has [VISUAL_GAP]
            if "[VISUAL_GAP]" in line_info['content']:
                print(f"   ⚠️  HAS [VISUAL_GAP] - This is why it's being detected as blank!")
                
                # Test the pattern matching
                label_patterns = [
                    r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*\[VISUAL_GAP\]',
                    r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*:\s*\[VISUAL_GAP\]',
                    r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s+\[VISUAL_GAP\]',
                ]
                
                for pattern_idx, pattern in enumerate(label_patterns):
                    matches = re.finditer(pattern, line_info['content'], re.IGNORECASE)
                    for match in matches:
                        potential_label = match.group(1).strip()
                        print(f"   Pattern {pattern_idx} matched: '{potential_label}'")
                        
                        # Check if it would be considered legitimate
                        is_legitimate = agent._is_legitimate_field_label(potential_label)
                        print(f"   Is legitimate field: {is_legitimate}")
                        
                        if potential_label.lower() == "mobile equipment subject":
                            print(f"   🎯 THIS IS THE PROBLEMATIC DETECTION!")
                            print(f"   This should be filtered out as static text, not a field label")
            else:
                print(f"   ✅ No [VISUAL_GAP] - correctly not detected as blank")
            print()
        
        # Check the context around these lines
        print("\n🔍 CONTEXT ANALYSIS:")
        print("=" * 60)
        
        for line_info in mobile_lines:
            if "[VISUAL_GAP]" in line_info['content']:
                line_num = line_info['line_num'] - 1  # Convert to 0-based
                print(f"Context around line {line_num + 1} (Page {line_info['page']}):")
                
                # Show 3 lines before and after
                start = max(0, line_num - 3)
                end = min(len(lines), line_num + 4)
                
                for i in range(start, end):
                    marker = ">>> " if i == line_num else "    "
                    print(f"{marker}{i+1:3d}: {lines[i]}")
                print()
        
        # Now run the actual blank field detection to see what gets detected
        print("\n🔍 ACTUAL BLANK FIELD DETECTION:")
        print("=" * 60)
        
        page_info = agent._extract_page_information(content, lines)
        blank_fields = agent._detect_blank_fields_with_gaps(lines, page_info)
        
        mobile_blank_fields = [f for f in blank_fields if "mobile equipment" in f['field_name'].lower()]
        
        print(f"Mobile Equipment blank fields detected: {len(mobile_blank_fields)}")
        for field in mobile_blank_fields:
            print(f"   Field: {field['field_name']}")
            print(f"   Type: {field['field_type']}")
            print(f"   Page: {field.get('page_number', 'N/A')}")
            print(f"   Reason: {field.get('blank_reason', 'N/A')}")
            print()
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_mobile_equipment())
