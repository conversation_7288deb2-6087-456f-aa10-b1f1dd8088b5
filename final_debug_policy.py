#!/usr/bin/env python3
"""
Final debug for policy number detection
"""

import re

def test_policy_patterns():
    """Test the policy number patterns"""
    
    print("🔍 TESTING POLICY NUMBER PATTERNS")
    print("=" * 60)
    
    # The actual line from page 6
    test_line = "POLICY NUMBER:                             :date:28/05/2025"
    
    print(f"Test line: '{test_line}'")
    print(f"Line length: {len(test_line)}")
    print()
    
    # Test patterns
    patterns = [
        r'POLICY NUMBER:\s+:date:',
        r'POLICY NUMBER:\s{10,}:date:',
        r'POLICY NUMBER:\s{5,}\w',
        r'POLICY NUMBER:\s{10,}',
    ]
    
    for i, pattern in enumerate(patterns, 1):
        match = re.search(pattern, test_line, re.IGNORECASE)
        print(f"Pattern {i}: {pattern}")
        print(f"   Match: {'✅ YES' if match else '❌ NO'}")
        if match:
            print(f"   Matched text: '{match.group()}'")
        print()
    
    # Analyze the spaces
    after_colon = test_line.split("POLICY NUMBER:")[1]
    print(f"After 'POLICY NUMBER:': '{after_colon}'")
    print(f"Length: {len(after_colon)}")
    
    # Count spaces before :date:
    before_date = after_colon.split(":date:")[0]
    print(f"Before ':date:': '{before_date}'")
    print(f"Length: {len(before_date)}")
    print(f"All spaces: {before_date == ' ' * len(before_date)}")
    
    # Test specific space count patterns
    space_patterns = [
        r'POLICY NUMBER:\s{5,}',
        r'POLICY NUMBER:\s{10,}',
        r'POLICY NUMBER:\s{15,}',
        r'POLICY NUMBER:\s{20,}',
        r'POLICY NUMBER:\s{25,}',
        r'POLICY NUMBER:\s{30,}',
    ]
    
    print("\n🔍 SPACE COUNT PATTERNS:")
    for pattern in space_patterns:
        match = re.search(pattern, test_line, re.IGNORECASE)
        print(f"   {pattern}: {'✅' if match else '❌'}")

if __name__ == "__main__":
    test_policy_patterns()
