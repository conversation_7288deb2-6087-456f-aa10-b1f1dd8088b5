#!/usr/bin/env python3
"""
Test backward compatibility with previous working results
"""

import asyncio
import os
from Document_Validation_Agentic import DocumentValidationAgent

async def test_backward_compatibility():
    """Test that enhanced logic maintains previous correct results"""
    
    print("🔍 TESTING BACKWARD COMPATIBILITY")
    print("=" * 80)
    
    # Check what PDF files we have available
    print("📁 Available PDF files:")
    
    sample_files = []
    generated_files = []
    
    if os.path.exists("sample"):
        for file in os.listdir("sample"):
            if file.endswith(".pdf"):
                sample_files.append(file)
                print(f"   Sample: {file}")
    
    if os.path.exists("generated"):
        for file in os.listdir("generated"):
            if file.endswith(".pdf"):
                generated_files.append(file)
                print(f"   Generated: {file}")
    
    print(f"\nFound {len(sample_files)} sample files and {len(generated_files)} generated files")
    
    # Test with current files
    if sample_files and generated_files:
        print(f"\n🧪 TESTING WITH CURRENT FILES:")
        print("=" * 60)
        
        sample_pdf = f"sample/{sample_files[0]}"
        generated_pdf = f"generated/{generated_files[0]}"
        
        print(f"Sample: {sample_pdf}")
        print(f"Generated: {generated_pdf}")
        
        try:
            # Initialize the validation agent in offline mode
            agent = DocumentValidationAgent(offline_mode=True)
            
            # Perform validation
            result = await agent.validate_documents(sample_pdf, generated_pdf)
            
            print(f"\n📊 CURRENT RESULTS:")
            print("=" * 40)
            print(f"Overall Score: {result.overall_score}/100")
            print(f"Dynamic Field Score: {result.dynamic_field_score}/100")
            print(f"Populated Fields: {len(result.populated_dynamic_fields)}")
            print(f"Blank Fields: {len(result.blank_dynamic_fields)}")
            
            print(f"\n✅ POPULATED FIELDS ({len(result.populated_dynamic_fields)}):")
            for i, field in enumerate(result.populated_dynamic_fields, 1):
                print(f"   {i}. {field['field_name']} = {field['field_value'][:50]}{'...' if len(field['field_value']) > 50 else ''}")
            
            print(f"\n🚨 BLANK FIELDS ({len(result.blank_dynamic_fields)}):")
            for i, field in enumerate(result.blank_dynamic_fields, 1):
                # Use business_impact instead of severity (after processing)
                severity = field.get('business_impact', field.get('severity', 'UNKNOWN'))
                print(f"   {i}. {field['field_name']} - {severity} (Page {field.get('page_number', 'N/A')})")
            
            # Check if we need to create test files for the policy documents
            print(f"\n💡 RECOMMENDATIONS:")
            print("=" * 40)
            
            if "PAP000000556" in sample_files[0]:
                print("✅ Testing with PAP insurance document")
                print("📝 This appears to be a life insurance policy with many form fields")
                print("🎯 Expected: Many blank fields should be detected for form completion")
                
                if len(result.blank_dynamic_fields) < 10:
                    print("⚠️  WARNING: Only detected few blank fields - may need enhancement")
                    print("🔧 The enhanced logic should detect more blank form fields")
                else:
                    print("✅ Good detection rate for form fields")
            
            elif "policy_1367076882" in sample_files[0]:
                print("✅ Testing with commercial auto policy document")
                print("📝 This appears to be a structured policy document")
                print("🎯 Expected: Specific blank fields like Policy Number on Page 6")
                
                # Check for specific expected results
                policy_number_blank = any("Policy Number" in field['field_name'] and "Page 6" in field['field_name'] 
                                        for field in result.blank_dynamic_fields)
                
                if policy_number_blank:
                    print("✅ Policy Number (Page 6) correctly detected as blank")
                else:
                    print("⚠️  WARNING: Policy Number (Page 6) not detected as blank")
                
                vertical_fields = [f for f in result.populated_dynamic_fields if 'Vertical layout' in f.get('location', '')]
                if vertical_fields:
                    print(f"✅ Vertical layout fields detected: {len(vertical_fields)}")
                else:
                    print("⚠️  WARNING: Vertical layout fields not detected")
            
            return result
            
        except Exception as e:
            print(f"❌ Error during validation: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    else:
        print("❌ No PDF files found for testing")
        return None

async def create_policy_test_files():
    """Create test files for the policy documents if needed"""
    
    print("\n🔧 CREATING POLICY TEST FILES FOR BACKWARD COMPATIBILITY")
    print("=" * 80)
    
    # This would be where we create the policy_1367076882 files if needed
    # For now, we'll work with the available files
    
    print("📝 Note: To test backward compatibility with policy_1367076882 files:")
    print("   1. Add policy_1367076882.pdf to sample/ folder")
    print("   2. Add policy_1367076882_generated.pdf to generated/ folder")
    print("   3. Re-run this test")

if __name__ == "__main__":
    result = asyncio.run(test_backward_compatibility())
    
    if result:
        print(f"\n🎯 BACKWARD COMPATIBILITY TEST SUMMARY:")
        print("=" * 80)
        print(f"✅ System is functional with current files")
        print(f"📊 Overall Score: {result.overall_score}/100")
        print(f"📊 Dynamic Field Score: {result.dynamic_field_score}/100")
        
        if result.overall_score >= 80:
            print("🏆 EXCELLENT: System performing well")
        elif result.overall_score >= 60:
            print("🥈 GOOD: System performing adequately")
        else:
            print("⚠️  NEEDS IMPROVEMENT: System needs enhancement")
        
        print(f"\n💡 Next steps:")
        print("   1. Verify enhanced logic doesn't break existing functionality")
        print("   2. Test with both document types (PAP and policy)")
        print("   3. Ensure comprehensive blank field detection")
    else:
        print(f"\n❌ BACKWARD COMPATIBILITY TEST FAILED")
        print("   System needs debugging before enhancement")
