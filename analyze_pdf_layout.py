#!/usr/bin/env python3
"""
PDF Layout Analysis Tool
Analyzes the structure and layout of PDF pages to detect column layouts
"""

import pdfplumber
import os

def analyze_pdf_layout(pdf_path: str):
    """Analyze PDF layout to understand column structure"""
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    print(f"📄 Analyzing PDF Layout: {pdf_path}")
    print("=" * 80)
    
    with pdfplumber.open(pdf_path) as pdf:
        for page_num, page in enumerate(pdf.pages):
            if page_num >= 7:  # Only analyze pages 2-7 as mentioned
                break
            if page_num < 1:  # Skip page 1
                continue
                
            print(f"\n📄 PAGE {page_num + 1} ANALYSIS")
            print("-" * 50)
            
            # Get page dimensions
            page_width = page.width
            page_height = page.height
            print(f"Page dimensions: {page_width:.1f} x {page_height:.1f}")
            
            # Analyze character positions to detect columns
            chars = page.chars
            if not chars:
                print("No characters found on this page")
                continue
            
            # Group characters by Y position (lines)
            lines_dict = {}
            for char in chars:
                y = round(char['y0'], 1)
                if y not in lines_dict:
                    lines_dict[y] = []
                lines_dict[y].append(char)
            
            # Analyze X positions to detect column structure
            x_positions = []
            for char in chars:
                x_positions.append(char['x0'])
            
            x_positions.sort()
            
            # Find potential column boundaries
            left_column_chars = [char for char in chars if char['x0'] < page_width / 2]
            right_column_chars = [char for char in chars if char['x0'] >= page_width / 2]
            
            print(f"Total characters: {len(chars)}")
            print(f"Left column characters: {len(left_column_chars)}")
            print(f"Right column characters: {len(right_column_chars)}")
            
            if len(left_column_chars) > 0 and len(right_column_chars) > 0:
                print("✅ COLUMN LAYOUT DETECTED")
                
                # Analyze the gap between columns
                left_max_x = max(char['x0'] + char.get('width', 5) for char in left_column_chars)
                right_min_x = min(char['x0'] for char in right_column_chars)
                column_gap = right_min_x - left_max_x
                
                print(f"Left column ends at: {left_max_x:.1f}")
                print(f"Right column starts at: {right_min_x:.1f}")
                print(f"Column gap size: {column_gap:.1f}")
                
                # Show sample content from each column
                print("\n📝 SAMPLE CONTENT:")
                print("Left Column (first 3 lines):")
                left_lines = {}
                for char in left_column_chars:
                    y = round(char['y0'], 1)
                    if y not in left_lines:
                        left_lines[y] = []
                    left_lines[y].append(char)
                
                sorted_left_lines = sorted(left_lines.items(), key=lambda x: -x[0])[:3]
                for y, line_chars in sorted_left_lines:
                    line_chars.sort(key=lambda c: c['x0'])
                    line_text = ''.join(char['text'] for char in line_chars)
                    print(f"  {line_text.strip()}")
                
                print("\nRight Column (first 3 lines):")
                right_lines = {}
                for char in right_column_chars:
                    y = round(char['y0'], 1)
                    if y not in right_lines:
                        right_lines[y] = []
                    right_lines[y].append(char)
                
                sorted_right_lines = sorted(right_lines.items(), key=lambda x: -x[0])[:3]
                for y, line_chars in sorted_right_lines:
                    line_chars.sort(key=lambda c: c['x0'])
                    line_text = ''.join(char['text'] for char in line_chars)
                    print(f"  {line_text.strip()}")
            else:
                print("❌ Single column layout detected")

if __name__ == "__main__":
    # Analyze the generated PDF
    pdf_path = "generated/policy_1367076882_generated.pdf"
    analyze_pdf_layout(pdf_path)
