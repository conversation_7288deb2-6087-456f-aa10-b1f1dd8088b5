#!/usr/bin/env python3
"""
Debug visual gap detection specifically
"""

import asyncio
import re
from Document_Validation_Agentic import DocumentValidationAgent

async def debug_visual_gap_detection():
    """Debug the visual gap detection specifically"""
    
    print("🔍 DEBUGGING VISUAL GAP DETECTION")
    print("=" * 80)
    
    # Initialize the validation agent in offline mode
    agent = DocumentValidationAgent(offline_mode=True)
    
    # Test files
    generated_pdf = "generated/PAP000000556_BlankValuesTest_Actual.pdf"
    
    print(f"📄 Generated PDF: {generated_pdf}")
    print("=" * 80)
    
    try:
        # Extract content first
        content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
        lines = content.split('\n')
        
        # Extract page information
        page_info = agent._extract_page_information(content, lines)
        
        print(f"📊 CONTENT ANALYSIS:")
        print(f"   Total lines: {len(lines)}")
        print(f"   Total pages: {page_info['total_pages']}")
        print(f"   Page boundaries: {len(page_info['page_boundaries'])}")
        
        # Find all lines with [VISUAL_GAP]
        visual_gap_lines = []
        for line_num, line in enumerate(lines):
            if "[VISUAL_GAP]" in line:
                page_number = page_info["line_to_page"].get(line_num, 1)
                visual_gap_lines.append({
                    'line_num': line_num,
                    'page': page_number,
                    'content': line.strip()
                })
        
        print(f"\n🔍 VISUAL GAP LINES FOUND: {len(visual_gap_lines)}")
        print("=" * 60)
        
        for i, gap_line in enumerate(visual_gap_lines[:10], 1):  # Show first 10
            print(f"{i:2d}. Page {gap_line['page']}, Line {gap_line['line_num']}: {gap_line['content']}")
        
        if len(visual_gap_lines) > 10:
            print(f"    ... and {len(visual_gap_lines) - 10} more")
        
        # Now test the enhanced detection logic manually
        print(f"\n🧪 TESTING ENHANCED DETECTION LOGIC:")
        print("=" * 60)
        
        # Test the patterns used in enhanced detection
        label_patterns = [
            r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*\[VISUAL_GAP\]',  # Label [VISUAL_GAP]
            r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*:\s*\[VISUAL_GAP\]',  # Label: [VISUAL_GAP]
            r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s+\[VISUAL_GAP\]',  # Label [VISUAL_GAP] (with space)
        ]
        
        potential_fields = []
        for gap_line in visual_gap_lines:
            line_content = gap_line['content']
            
            for pattern_idx, pattern in enumerate(label_patterns):
                matches = re.finditer(pattern, line_content, re.IGNORECASE)
                for match in matches:
                    potential_label = match.group(1).strip()
                    
                    # Apply the same filtering logic as in the enhanced detection
                    excluded_patterns = [
                        "read the", "duties and what", "refer to", "the words", "you and your",
                        "we us and our", "various provisions", "entire policy", "throughout this policy",
                        "carefully to determine", "section i", "covered autos", "declarations"
                    ]
                    
                    is_excluded = any(pattern in potential_label.lower() for pattern in excluded_patterns)
                    
                    # Check if it's a legitimate field label
                    is_legitimate = agent._is_legitimate_field_label(potential_label)
                    
                    # Check length and other criteria
                    meets_criteria = (
                        len(potential_label) > 2 and
                        len(potential_label) < 60 and
                        not potential_label.isupper() and
                        not re.match(r'^\d+\s', potential_label)
                    )
                    
                    potential_fields.append({
                        'page': gap_line['page'],
                        'line_num': gap_line['line_num'],
                        'label': potential_label,
                        'pattern_idx': pattern_idx,
                        'is_excluded': is_excluded,
                        'is_legitimate': is_legitimate,
                        'meets_criteria': meets_criteria,
                        'would_be_detected': not is_excluded and is_legitimate and meets_criteria,
                        'full_line': line_content
                    })
        
        print(f"Found {len(potential_fields)} potential field labels:")
        
        # Show results
        detected_count = 0
        for i, field in enumerate(potential_fields, 1):
            status = "✅ WOULD DETECT" if field['would_be_detected'] else "❌ WOULD SKIP"
            if field['would_be_detected']:
                detected_count += 1
            
            print(f"{i:2d}. {status} - Page {field['page']}: {field['label']}")
            print(f"    Pattern {field['pattern_idx']}, Excluded: {field['is_excluded']}, Legitimate: {field['is_legitimate']}, Criteria: {field['meets_criteria']}")
            print(f"    Line: {field['full_line']}")
            print()
        
        print(f"📊 DETECTION SUMMARY:")
        print(f"   Visual gap lines: {len(visual_gap_lines)}")
        print(f"   Potential labels: {len(potential_fields)}")
        print(f"   Would be detected: {detected_count}")
        print(f"   Detection rate: {detected_count / len(potential_fields) * 100:.1f}%" if potential_fields else "0%")
        
        # Now run the actual detection to compare
        print(f"\n🔍 ACTUAL DETECTION RESULTS:")
        print("=" * 60)
        
        blank_fields = agent._detect_blank_fields_with_gaps(lines, page_info)
        
        print(f"Actually detected blank fields: {len(blank_fields)}")
        for i, field in enumerate(blank_fields, 1):
            print(f"{i:2d}. {field['field_name']} - {field['severity']} (Page {field.get('page_number', 'N/A')})")
        
        print(f"\n📊 COMPARISON:")
        print(f"   Expected detections: {detected_count}")
        print(f"   Actual detections: {len(blank_fields)}")
        print(f"   Match rate: {len(blank_fields) / max(detected_count, 1) * 100:.1f}%")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_visual_gap_detection())
