#!/usr/bin/env python3
"""
Comprehensive analysis to find missing blank dynamic fields
"""

import asyncio
import re
from Document_Validation_Agentic import EnhancedPDFProcessor

async def analyze_missing_blank_fields():
    """Analyze what blank fields are being missed"""
    
    print("🔍 COMPREHENSIVE BLANK FIELD ANALYSIS")
    print("=" * 80)
    
    processor = EnhancedPDFProcessor()
    
    # Test with the generated PDF
    pdf_path = "generated/PAP000000556_BlankValuesTest_Actual.pdf"
    
    print(f"📄 Analyzing: {pdf_path}")
    print("=" * 80)
    
    try:
        content = await processor.extract_content_with_layout(pdf_path)
        lines = content.split('\n')
        
        print("🔍 SEARCHING FOR ALL POTENTIAL BLANK FIELDS")
        print("=" * 60)
        
        # Common field label patterns that might indicate blank fields
        field_patterns = [
            r'([A-Za-z\s]+):\s*\[VISUAL_GAP\]',  # Label: [VISUAL_GAP]
            r'([A-Za-z\s]+):\s*$',  # Label: (end of line - might be blank)
            r'([A-Za-z\s]+):\s{5,}',  # Label: (5+ spaces - likely blank)
            r'([A-Za-z\s]+):\s*_+',  # Label: _____ (underscores for filling)
            r'([A-Za-z\s]+):\s*\.+',  # Label: ..... (dots for filling)
            r'([A-Za-z\s]+):\s*\$\s*$',  # Label: $ (currency field with no amount)
            r'([A-Za-z\s]+):\s*\$\s*\[VISUAL_GAP\]',  # Label: $ [VISUAL_GAP]
        ]
        
        potential_blank_fields = []
        
        for line_num, line in enumerate(lines, 1):
            line_stripped = line.strip()
            
            # Find which page this line is on
            page_num = "Unknown"
            for i in range(line_num - 1, -1, -1):
                if "--- Page" in lines[i]:
                    page_num = lines[i].split("Page")[1].split("---")[0].strip()
                    break
            
            # Skip column headers and table content
            if line_stripped in ["[LEFT COLUMN]", "[RIGHT COLUMN]"] or line_stripped.startswith("[TABLE_CONTENT]"):
                continue
            
            # Check each pattern
            for pattern in field_patterns:
                matches = re.finditer(pattern, line_stripped, re.IGNORECASE)
                for match in matches:
                    potential_label = match.group(1).strip()
                    
                    # Filter out obvious non-fields
                    if (len(potential_label) > 2 and 
                        len(potential_label) < 50 and
                        not potential_label.lower() in ['the', 'and', 'or', 'of', 'in', 'to', 'for', 'with'] and
                        not potential_label.isupper() and  # Avoid section headers
                        ':' not in potential_label):  # Avoid nested colons
                        
                        potential_blank_fields.append({
                            'line_num': line_num,
                            'page': page_num,
                            'label': potential_label,
                            'full_line': line_stripped,
                            'pattern': pattern
                        })
        
        print(f"📊 FOUND {len(potential_blank_fields)} POTENTIAL BLANK FIELDS:")
        print("=" * 60)
        
        # Group by page
        by_page = {}
        for field in potential_blank_fields:
            page = field['page']
            if page not in by_page:
                by_page[page] = []
            by_page[page].append(field)
        
        for page in sorted(by_page.keys(), key=lambda x: int(x) if x.isdigit() else 999):
            print(f"\n📄 PAGE {page}:")
            print("-" * 40)
            
            for i, field in enumerate(by_page[page], 1):
                print(f"   {i}. {field['label']}")
                print(f"      Line {field['line_num']}: {field['full_line']}")
                print(f"      Pattern: {field['pattern']}")
                print()
        
        print("\n🔍 SPECIFIC FIELD TYPE ANALYSIS:")
        print("=" * 60)
        
        # Look for specific types of fields that are commonly blank
        specific_searches = [
            ("Policy Numbers", [r'POLICY\s+NUMBER', r'Policy\s+Number', r'POLICY\s+NO']),
            ("Names", [r'NAME', r'NAMED\s+INSURED', r'INSURED\s+NAME', r'AGENT\s+NAME']),
            ("Addresses", [r'ADDRESS', r'MAILING\s+ADDRESS', r'BUSINESS\s+ADDRESS']),
            ("Dates", [r'DATE', r'EFFECTIVE\s+DATE', r'EXPIRATION\s+DATE', r'ISSUE\s+DATE']),
            ("Phone Numbers", [r'PHONE', r'TELEPHONE', r'FAX']),
            ("Amounts", [r'PREMIUM', r'AMOUNT', r'LIMIT', r'DEDUCTIBLE']),
            ("Signatures", [r'SIGNATURE', r'SIGNED', r'AUTHORIZED']),
            ("Endorsements", [r'ENDORSEMENT', r'ENDT', r'FORM\s+NUMBER']),
        ]
        
        for category, patterns in specific_searches:
            print(f"\n🔍 {category.upper()}:")
            found_any = False
            
            for line_num, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Find page
                page_num = "Unknown"
                for i in range(line_num - 1, -1, -1):
                    if "--- Page" in lines[i]:
                        page_num = lines[i].split("Page")[1].split("---")[0].strip()
                        break
                
                for pattern in patterns:
                    if re.search(pattern, line_stripped, re.IGNORECASE):
                        print(f"   Page {page_num}, Line {line_num}: {line_stripped}")
                        found_any = True
            
            if not found_any:
                print(f"   No {category.lower()} found")
        
        print("\n🔍 VISUAL GAP ANALYSIS:")
        print("=" * 60)
        
        visual_gap_lines = []
        for line_num, line in enumerate(lines, 1):
            if "[VISUAL_GAP]" in line:
                # Find page
                page_num = "Unknown"
                for i in range(line_num - 1, -1, -1):
                    if "--- Page" in lines[i]:
                        page_num = lines[i].split("Page")[1].split("---")[0].strip()
                        break
                
                visual_gap_lines.append({
                    'line_num': line_num,
                    'page': page_num,
                    'content': line.strip()
                })
        
        print(f"Found {len(visual_gap_lines)} lines with [VISUAL_GAP] markers:")
        
        # Group by page
        gap_by_page = {}
        for gap in visual_gap_lines:
            page = gap['page']
            if page not in gap_by_page:
                gap_by_page[page] = []
            gap_by_page[page].append(gap)
        
        for page in sorted(gap_by_page.keys(), key=lambda x: int(x) if x.isdigit() else 999):
            print(f"\n📄 PAGE {page} - {len(gap_by_page[page])} gaps:")
            for gap in gap_by_page[page][:5]:  # Show first 5
                print(f"   Line {gap['line_num']}: {gap['content']}")
            if len(gap_by_page[page]) > 5:
                print(f"   ... and {len(gap_by_page[page]) - 5} more")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")

if __name__ == "__main__":
    asyncio.run(analyze_missing_blank_fields())
