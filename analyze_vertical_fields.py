#!/usr/bin/env python3
"""
Analyze vertical field layout patterns in pages 8, 9, 10
"""

import asyncio
from Document_Validation_Agentic import EnhancedPDFProcessor

async def analyze_vertical_fields():
    """Analyze vertical field layout patterns"""
    
    print("🔍 ANALYZING VERTICAL FIELD LAYOUTS")
    print("=" * 80)
    
    processor = EnhancedPDFProcessor()
    
    # Test with the generated PDF
    pdf_path = "generated/policy_1367076882_generated.pdf"
    
    print(f"📄 Analyzing: {pdf_path}")
    print("=" * 80)
    
    try:
        content = await processor.extract_content_with_layout(pdf_path)
        lines = content.split('\n')
        
        # Analyze pages 8, 9, 10
        for page_num in [8, 9, 10]:
            print(f"\n📄 PAGE {page_num} VERTICAL FIELD ANALYSIS:")
            print("-" * 60)
            
            page_start = None
            page_end = None
            
            for i, line in enumerate(lines):
                if f"--- Page {page_num} ---" in line:
                    page_start = i
                elif f"--- Page {page_num + 1} ---" in line:
                    page_end = i
                    break
            
            if page_start is not None:
                end_line = page_end if page_end else len(lines)
                page_lines = lines[page_start:end_line]
                
                print(f"📝 Full Page {page_num} Content:")
                for i, line in enumerate(page_lines):
                    if line.strip():
                        print(f"{i+1:3d}: {line}")
                
                print(f"\n🔍 VERTICAL FIELD PATTERN ANALYSIS:")
                print("-" * 40)
                
                # Look for vertical field patterns
                # Pattern: Field label line followed by value line(s)
                for i, line in enumerate(page_lines):
                    line_stripped = line.strip()
                    
                    # Look for field label patterns
                    if any(pattern in line_stripped for pattern in [
                        "ENDT. NO.", "POLICY NO.", "ISSUED TO", "EFFECTIVE", "EXPIRATION"
                    ]):
                        print(f"📋 Found field label at line {i+1}: {line_stripped}")
                        
                        # Check the next few lines for values
                        for j in range(1, 5):  # Check next 4 lines
                            if i + j < len(page_lines):
                                next_line = page_lines[i + j].strip()
                                if next_line and not next_line.startswith("[") and len(next_line) > 2:
                                    print(f"   📝 Potential value at line {i+j+1}: {next_line}")
                                    break
                        print()
                
                # Look for [VISUAL_GAP] patterns and what follows them
                print(f"🔍 VISUAL GAP ANALYSIS:")
                print("-" * 40)
                
                for i, line in enumerate(page_lines):
                    if "[VISUAL_GAP]" in line:
                        print(f"📍 Visual gap at line {i+1}: {line.strip()}")
                        
                        # Check lines below for values
                        for j in range(1, 4):
                            if i + j < len(page_lines):
                                next_line = page_lines[i + j].strip()
                                if next_line and not next_line.startswith("["):
                                    print(f"   ⬇️  Line {i+j+1} below: {next_line}")
                        print()
            else:
                print(f"❌ Page {page_num}: Not found in document")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")

if __name__ == "__main__":
    asyncio.run(analyze_vertical_fields())
