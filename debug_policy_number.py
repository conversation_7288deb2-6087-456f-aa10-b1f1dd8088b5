#!/usr/bin/env python3
"""
Debug script to analyze policy number detection on page 6
"""

import asyncio
from Document_Validation_Agentic import EnhancedPDFProcessor

async def debug_policy_number():
    """Debug policy number detection"""
    
    print("🔍 DEBUGGING POLICY NUMBER DETECTION")
    print("=" * 80)
    
    processor = EnhancedPDFProcessor()
    
    # Test with the generated PDF
    pdf_path = "generated/policy_1367076882_generated.pdf"
    
    print(f"📄 Analyzing: {pdf_path}")
    print("=" * 80)
    
    try:
        content = await processor.extract_content_with_layout(pdf_path)
        
        # Find page 6 content
        lines = content.split('\n')
        page_6_start = None
        page_7_start = None
        
        for i, line in enumerate(lines):
            if "--- Page 6 ---" in line:
                page_6_start = i
            elif "--- Page 7 ---" in line:
                page_7_start = i
                break
        
        if page_6_start is not None:
            print("📄 PAGE 6 CONTENT ANALYSIS:")
            print("-" * 50)
            
            end_line = page_7_start if page_7_start else len(lines)
            page_6_lines = lines[page_6_start:end_line]
            
            # Show all lines from page 6
            for i, line in enumerate(page_6_lines):
                if line.strip():
                    print(f"{i+1:3d}: {line}")
            
            print("\n🔍 SEARCHING FOR POLICY NUMBER PATTERNS:")
            print("-" * 50)
            
            # Look for policy number patterns
            policy_patterns = [
                "POLICY NUMBER",
                "Policy Number",
                "Policy #",
                "POLICY #",
                "[VISUAL_GAP]"
            ]
            
            for pattern in policy_patterns:
                found_lines = []
                for i, line in enumerate(page_6_lines):
                    if pattern.lower() in line.lower():
                        found_lines.append((i+1, line.strip()))
                
                if found_lines:
                    print(f"✅ Found '{pattern}' in {len(found_lines)} lines:")
                    for line_num, line_content in found_lines:
                        print(f"   Line {line_num}: {line_content}")
                else:
                    print(f"❌ Pattern '{pattern}' not found")
            
            print("\n🔍 VISUAL GAP ANALYSIS:")
            print("-" * 50)
            
            visual_gap_lines = []
            for i, line in enumerate(page_6_lines):
                if "[VISUAL_GAP]" in line:
                    visual_gap_lines.append((i+1, line.strip()))
            
            if visual_gap_lines:
                print(f"Found {len(visual_gap_lines)} lines with [VISUAL_GAP]:")
                for line_num, line_content in visual_gap_lines:
                    print(f"   Line {line_num}: {line_content}")
            else:
                print("No [VISUAL_GAP] markers found on page 6")
        
        # Also check pages 8, 9, 10 for dynamic fields
        print("\n📄 CHECKING PAGES 8-10 FOR DYNAMIC FIELDS:")
        print("-" * 50)
        
        for page_num in [8, 9, 10]:
            page_start = None
            page_end = None
            
            for i, line in enumerate(lines):
                if f"--- Page {page_num} ---" in line:
                    page_start = i
                elif f"--- Page {page_num + 1} ---" in line:
                    page_end = i
                    break
            
            if page_start is not None:
                end_line = page_end if page_end else len(lines)
                page_lines = lines[page_start:end_line]
                
                # Look for dynamic field patterns
                dynamic_patterns = [
                    ":",  # Field labels with colons
                    "[VISUAL_GAP]",  # Blank fields
                    r"\d{2}/\d{2}/\d{4}",  # Dates
                    r"\$[\d,]+",  # Currency
                    "Name",
                    "Address",
                    "Phone",
                    "Premium",
                    "Amount"
                ]
                
                found_patterns = []
                for pattern in dynamic_patterns:
                    for line in page_lines:
                        if pattern in line and line.strip():
                            found_patterns.append(line.strip())
                            break
                
                if found_patterns:
                    print(f"✅ Page {page_num}: Found {len(found_patterns)} potential dynamic fields")
                    for pattern in found_patterns[:3]:  # Show first 3
                        print(f"   {pattern}")
                else:
                    print(f"❌ Page {page_num}: No dynamic field patterns found")
            else:
                print(f"❌ Page {page_num}: Not found in document")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")

if __name__ == "__main__":
    asyncio.run(debug_policy_number())
