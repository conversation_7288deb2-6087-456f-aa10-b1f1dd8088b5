#!/usr/bin/env python3
"""
Debug the remaining false positives and missing policy number detection
"""

import asyncio
from Document_Validation_Agentic import EnhancedPDFProcessor

async def debug_remaining_issues():
    """Debug the specific remaining issues"""
    
    print("🔍 DEBUGGING REMAINING VALIDATION ISSUES")
    print("=" * 80)
    
    processor = EnhancedPDFProcessor()
    
    # Test with the generated PDF
    pdf_path = "generated/policy_1367076882_generated.pdf"
    
    print(f"📄 Analyzing: {pdf_path}")
    print("=" * 80)
    
    try:
        content = await processor.extract_content_with_layout(pdf_path)
        lines = content.split('\n')
        
        print("🔍 ISSUE 1: FALSE POSITIVE BLANK FIELDS")
        print("=" * 60)
        print("Looking for the false positive patterns...")
        
        false_positive_patterns = ["Read the", "duties and what", "refer to", "The words"]
        
        for pattern in false_positive_patterns:
            print(f"\n🔍 Searching for '{pattern}':")
            found_lines = []
            for i, line in enumerate(lines):
                if pattern.lower() in line.lower() and "[VISUAL_GAP]" in line:
                    page_num = "Unknown"
                    # Find which page this line is on
                    for j in range(i, -1, -1):
                        if "--- Page" in lines[j]:
                            page_num = lines[j].split("Page")[1].split("---")[0].strip()
                            break
                    found_lines.append((i+1, line.strip(), page_num))
            
            if found_lines:
                print(f"   ✅ Found {len(found_lines)} occurrences:")
                for line_num, line_content, page in found_lines:
                    print(f"      Line {line_num} (Page {page}): {line_content}")
                    
                    # Check if this is in a column layout context
                    context_start = max(0, line_num - 5)
                    context_end = min(len(lines), line_num + 3)
                    print(f"      Context (lines {context_start}-{context_end}):")
                    for ctx_i in range(context_start, context_end):
                        if ctx_i < len(lines):
                            marker = ">>> " if ctx_i == line_num - 1 else "    "
                            print(f"      {marker}{ctx_i+1}: {lines[ctx_i].strip()}")
                    print()
            else:
                print(f"   ❌ Pattern '{pattern}' not found")
        
        print("\n🔍 ISSUE 2: MISSING POLICY NUMBER ON PAGE 6")
        print("=" * 60)
        
        # Find page 6 content
        page_6_start = None
        page_7_start = None
        
        for i, line in enumerate(lines):
            if "--- Page 6 ---" in line:
                page_6_start = i
            elif "--- Page 7 ---" in line:
                page_7_start = i
                break
        
        if page_6_start is not None:
            print("📄 PAGE 6 DETAILED ANALYSIS:")
            print("-" * 40)
            
            end_line = page_7_start if page_7_start else len(lines)
            page_6_lines = lines[page_6_start:end_line]
            
            # Show all lines from page 6 with line numbers
            for i, line in enumerate(page_6_lines):
                if line.strip():
                    print(f"{i+1:3d}: {line}")
            
            print("\n🔍 POLICY NUMBER PATTERN ANALYSIS:")
            print("-" * 40)
            
            # Look for the specific policy number pattern
            for i, line in enumerate(page_6_lines):
                if "POLICY NUMBER" in line.upper():
                    print(f"✅ Found POLICY NUMBER at line {i+1}: {line}")
                    
                    # Analyze the pattern in detail
                    if ":date:" in line:
                        print("   🔍 Pattern contains ':date:' - this indicates a gap!")
                        print("   📝 Full pattern analysis:")
                        
                        # Split the line to see the gap
                        parts = line.split("POLICY NUMBER:")
                        if len(parts) > 1:
                            after_label = parts[1]
                            print(f"   📍 After 'POLICY NUMBER:': '{after_label}'")
                            
                            # Check for large spaces or gaps
                            if ":date:" in after_label:
                                gap_part = after_label.split(":date:")[0]
                                print(f"   📏 Gap before ':date:': '{gap_part}' (length: {len(gap_part)})")
                                
                                if len(gap_part.strip()) == 0 and len(gap_part) > 10:
                                    print("   ✅ LARGE GAP DETECTED - This should be flagged as blank!")
                                else:
                                    print("   ❌ Gap not large enough or contains content")
                    
                    # Check if [VISUAL_GAP] marker is present
                    if "[VISUAL_GAP]" in line:
                        print("   ✅ [VISUAL_GAP] marker found - should be detected")
                    else:
                        print("   ❌ [VISUAL_GAP] marker NOT found - this is the issue!")
                        print("   💡 The gap detection logic is not creating the marker")
        
        print("\n🔍 ISSUE 3: COLUMN LAYOUT CONTEXT ANALYSIS")
        print("=" * 60)
        
        # Analyze column layout markers
        left_column_count = content.count("[LEFT COLUMN]")
        right_column_count = content.count("[RIGHT COLUMN]")
        table_content_count = content.count("[TABLE_CONTENT]")
        
        print(f"Column layout markers found:")
        print(f"   [LEFT COLUMN]: {left_column_count}")
        print(f"   [RIGHT COLUMN]: {right_column_count}")
        print(f"   [TABLE_CONTENT]: {table_content_count}")
        
        # Show context around false positive patterns
        print(f"\n🔍 CONTEXT ANALYSIS FOR FALSE POSITIVES:")
        print("-" * 40)
        
        for i, line in enumerate(lines):
            if any(fp in line.lower() for fp in ["read the", "duties and what"]) and "[VISUAL_GAP]" in line:
                print(f"False positive at line {i+1}: {line.strip()}")
                
                # Check surrounding context for column markers
                context_range = range(max(0, i-3), min(len(lines), i+4))
                for ctx_i in context_range:
                    if ctx_i < len(lines):
                        marker = ">>> " if ctx_i == i else "    "
                        print(f"{marker}{ctx_i+1}: {lines[ctx_i].strip()}")
                print()
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")

if __name__ == "__main__":
    asyncio.run(debug_remaining_issues())
