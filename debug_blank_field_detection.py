#!/usr/bin/env python3
"""
Debug blank field detection to see why many fields are missed
"""

import asyncio
import re
from Document_Validation_Agentic import DocumentValidationAgent

async def debug_blank_field_detection():
    """Debug the blank field detection process"""
    
    print("🔍 DEBUGGING BLANK FIELD DETECTION")
    print("=" * 80)
    
    # Initialize the validation agent in offline mode
    agent = DocumentValidationAgent(offline_mode=True)
    
    # Test files
    sample_pdf = "sample/PAP000000556_BlankValuesTest.pdf"
    generated_pdf = "generated/PAP000000556_BlankValuesTest_Actual.pdf"
    
    print(f"📄 Sample PDF: {sample_pdf}")
    print(f"📄 Generated PDF: {generated_pdf}")
    print("=" * 80)
    
    try:
        # Extract content first
        content = await agent.pdf_processor.extract_content_with_layout(generated_pdf)
        lines = content.split('\n')
        
        print("🔍 MANUAL VISUAL GAP ANALYSIS:")
        print("=" * 60)
        
        # Find all lines with [VISUAL_GAP]
        visual_gap_lines = []
        for line_num, line in enumerate(lines, 1):
            if "[VISUAL_GAP]" in line:
                # Find page
                page_num = "Unknown"
                for i in range(line_num - 1, -1, -1):
                    if "--- Page" in lines[i]:
                        page_num = lines[i].split("Page")[1].split("---")[0].strip()
                        break
                
                visual_gap_lines.append({
                    'line_num': line_num,
                    'page': page_num,
                    'content': line.strip()
                })
        
        print(f"Found {len(visual_gap_lines)} lines with [VISUAL_GAP]:")
        
        # Analyze each line for potential field labels
        potential_fields = []
        for gap_line in visual_gap_lines:
            line_content = gap_line['content']
            
            # Test the same patterns used in the detection logic
            label_patterns = [
                r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*\[VISUAL_GAP\]',
                r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s*:\s*\[VISUAL_GAP\]',
                r'([A-Za-z][A-Za-z\s]*[A-Za-z])\s+\[VISUAL_GAP\]',
            ]
            
            for pattern in label_patterns:
                matches = re.finditer(pattern, line_content, re.IGNORECASE)
                for match in matches:
                    potential_label = match.group(1).strip()
                    
                    # Apply the same filtering logic
                    excluded_patterns = [
                        "read the", "duties and what", "refer to", "the words", "you and your",
                        "we us and our", "various provisions", "entire policy", "throughout this policy",
                        "carefully to determine", "section i", "covered autos", "declarations"
                    ]
                    
                    is_excluded = any(pattern in potential_label.lower() for pattern in excluded_patterns)
                    
                    potential_fields.append({
                        'page': gap_line['page'],
                        'line_num': gap_line['line_num'],
                        'label': potential_label,
                        'full_line': line_content,
                        'pattern': pattern,
                        'excluded': is_excluded,
                        'length': len(potential_label),
                        'is_upper': potential_label.isupper(),
                        'word_count': potential_label.count(' ') + 1
                    })
        
        print(f"\n📊 FOUND {len(potential_fields)} POTENTIAL FIELD LABELS:")
        print("=" * 60)
        
        for i, field in enumerate(potential_fields, 1):
            status = "❌ EXCLUDED" if field['excluded'] else "✅ VALID"
            print(f"{i:2d}. {status} - Page {field['page']}: {field['label']}")
            print(f"    Line {field['line_num']}: {field['full_line']}")
            print(f"    Length: {field['length']}, Words: {field['word_count']}, Upper: {field['is_upper']}")
            print()
        
        # Now run the actual validation to see what gets detected
        print("\n🔍 ACTUAL VALIDATION RESULTS:")
        print("=" * 60)
        
        result = await agent.validate_documents(sample_pdf, generated_pdf)
        
        print(f"Detected blank fields: {len(result.blank_dynamic_fields)}")
        for i, field in enumerate(result.blank_dynamic_fields, 1):
            print(f"{i}. {field['field_name']} - {field['severity']} (Page {field.get('page_number', 'N/A')})")
        
        print(f"\nDetected populated fields: {len(result.populated_dynamic_fields)}")
        for i, field in enumerate(result.populated_dynamic_fields, 1):
            print(f"{i}. {field['field_name']} - Page {field.get('page_number', 'N/A')}")
        
        # Compare what should be detected vs what was detected
        print(f"\n📊 COMPARISON:")
        print("=" * 60)
        print(f"Lines with [VISUAL_GAP]: {len(visual_gap_lines)}")
        print(f"Potential field labels found: {len([f for f in potential_fields if not f['excluded']])}")
        print(f"Actually detected blank fields: {len(result.blank_dynamic_fields)}")
        print(f"Detection rate: {len(result.blank_dynamic_fields) / len([f for f in potential_fields if not f['excluded']]) * 100:.1f}%")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_blank_field_detection())
