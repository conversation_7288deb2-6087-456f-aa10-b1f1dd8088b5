🚀 DOCUMENT VALIDATION REPORT
============================================================
Validation Date: 2025-06-11T08:09:48.402695
Processing Time: 1.56 seconds
Overall Score: 37/100
Validation Status: FAIL

📊 DETAILED SCORES:
   Static Text Compliance: 70/100
   Dynamic Field Completeness: 19/100

🔍 STATIC TEXT ANALYSIS:
   Differences Found: 3
   1. DIFFERENT_TEXT:
      Location: Line 40
      Severity: LOW
      Sample: "Business Phone"
      Generated: "Mobile Phone"
      Impact: Minor text variation

   2. DIFFERENT_TEXT:
      Location: Line 41
      Severity: LOW
      Sample: "Mobile Phone"
      Generated: "Home Phone"
      Impact: Minor text variation

   3. DIFFERENT_TEXT:
      Location: Line 78
      Severity: MEDIUM
      Sample: "The Contract"
      Generated: "agreed upon in writing after this policy is issued shall constitute the entire"
      Impact: Significant text difference that should be reviewed

✅ POPULATED DYNAMIC FIELDS ANALYSIS:
   Total Populated Fields: 7

   1. COMPANY NAME (PAGE 3):
      Type: company_name_page_3
      Value: for an Insurance Policy an have reviewed the 
provisions shoiwng how a life insurance policy performs using the company
      Location: Page 3
      Page Number: 3

   2. COMPANY NAME (PAGE 4):
      Type: company_name_page_4
      Value: No liability shall be borned by the Company
      Location: Page 4
      Page Number: 4

   3. ORGANIZATION NAME (PAGE 1):
      Type: organization_name_page_1
      Value: Estimated Annual Inc
      Location: Page 1
      Page Number: 1

   4. ORGANIZATION NAME (PAGE 2):
      Type: organization_name_page_2
      Value: Estimated Annual Inc
      Location: Page 2
      Page Number: 2

   5. ORGANIZATION NAME (PAGE 3):
      Type: organization_name_page_3
      Value: Inc
      Location: Page 3
      Page Number: 3

   6. ORGANIZATION NAME (PAGE 4):
      Type: organization_name_page_4
      Value: Acceptance of placement of payments shall be at any of our 
offices or such other location as determined by us from time to time
After two years from the time of effectivity or from last reinstatement of the 
Inc
      Location: Page 4
      Page Number: 4

   7. ORGANIZATION NAME (PAGE 5):
      Type: organization_name_page_5
      Value: Signature of Corp
      Location: Page 5
      Page Number: 5

🚨 BLANK DYNAMIC FIELDS ANALYSIS:
   Total Blank Fields: 29
   Critical Issues: 8

   1. POLICY NUMBER (PAGE 1):
      Type: policy_number_page_1
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Policy Number field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Policy Number (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   2. DATE (PAGE 1):
      Type: date_page_1
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Date field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Date (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   3. APPLICANT (PAGE 1):
      Type: applicant_page_1
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Applicant field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Applicant (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   4. GENDER OF APPLICANT (PAGE 1):
      Type: gender_of_applicant_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Gender of Applicant field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Gender of Applicant (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   5. RESIDENCE ADDRESS OF APPLICANT (PAGE 1):
      Type: residence_address_of_applicant_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Residence Address of Applicant field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Residence Address of Applicant (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   6. BIRTHDAY OF APPLICANT (PAGE 1):
      Type: birthday_of_applicant_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Birthday of Applicant field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Birthday of Applicant (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   7. AGE OF THE APPLICANT AT ISSUANCE OF (PAGE 1):
      Type: age_of_the_applicant_at_issuance_of_page_1
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Age of the Applicant at issuance of field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Age of the Applicant at issuance of (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   8. NAME OF EMPLOYER (PAGE 1):
      Type: name_of_employer_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Name of Employer field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Name of Employer (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   9. NATURE OF BUSINESS (PAGE 1):
      Type: nature_of_business_page_1
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Nature of Business field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Nature of Business (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   10. HOME PHONE (PAGE 1):
      Type: home_phone_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Home Phone field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Home Phone (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   11. BUSINESS PHONE (PAGE 1):
      Type: business_phone_page_1
      Status: BLANK
      Business Impact: HIGH
      Impact: Business Phone field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Business Phone (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   12. NAME OF LIFE INSURED (PAGE 1):
      Type: name_of_life_insured_page_1
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Name of Life Insured field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Name of Life Insured (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   13. GENDER OF INSURED (PAGE 1):
      Type: gender_of_insured_page_1
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Gender of Insured field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Gender of Insured (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   14. RESIDENCE ADDRESS OF INSURED (PAGE 1):
      Type: residence_address_of_insured_page_1
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Residence Address of Insured field on page 1 is blank - contains visual gap indicating missing content
      Recommendation: Populate Residence Address of Insured (Page 1) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   15. NAME OF EMPLOYER (PAGE 2):
      Type: name_of_employer_page_2
      Status: BLANK
      Business Impact: HIGH
      Impact: Name of Employer field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Name of Employer (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   16. NATURE OF BUSINESS (PAGE 2):
      Type: nature_of_business_page_2
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Nature of Business field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Nature of Business (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   17. EFFECTIVITY DATE (PAGE 2):
      Type: effectivity_date_page_2
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Effectivity Date field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Effectivity Date (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   18. NAMED PRIMARY BENEFICIARY (PAGE 2):
      Type: named_primary_beneficiary_page_2
      Status: BLANK
      Business Impact: HIGH
      Impact: Named Primary Beneficiary field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Named Primary Beneficiary (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   19. RELATIONSHIP (PAGE 2):
      Type: relationship_page_2
      Status: BLANK
      Business Impact: HIGH
      Impact: Relationship field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Relationship (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   20. NAMED SECONDARY BENEFICIARY (PAGE 2):
      Type: named_secondary_beneficiary_page_2
      Status: BLANK
      Business Impact: HIGH
      Impact: Named Secondary Beneficiary field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Named Secondary Beneficiary (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   21. ACCIDENTAL DEATH (PAGE 2):
      Type: accidental_death_page_2
      Status: BLANK
      Business Impact: MEDIUM
      Impact: Accidental Death field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Accidental Death (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   22. TOTAL ANNUAL PREMIUM PAYABLE (PAGE 2):
      Type: total_annual_premium_payable_page_2
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Total Annual Premium Payable field on page 2 is blank - contains visual gap indicating missing content
      Recommendation: Populate Total Annual Premium Payable (Page 2) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   23. SIGNATURE OF PRESIDENT (PAGE 5):
      Type: signature_of_president_page_5
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Signature of President field on page 5 is blank - contains visual gap indicating missing content
      Recommendation: Populate Signature of President (Page 5) with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   24. POLICY NUMBER:
      Type: policy_number
      Status: BLANK
      Business Impact: CRITICAL
      Impact: Policy number appears to be missing - collapsed with date field
      Recommendation: Populate Policy Number with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   25. BASIC BENEFIT PREMIUM:
      Type: basic_benefit_premium
      Status: BLANK
      Business Impact: HIGH
      Impact: Basic Benefit Premium amount is missing
      Recommendation: Populate Basic Benefit Premium with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   26. TOTAL DISABILITY:
      Type: total_disability
      Status: BLANK
      Business Impact: HIGH
      Impact: Total Disability amount is missing
      Recommendation: Populate Total Disability with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   27. BUSINESS PHONE:
      Type: business_phone
      Status: BLANK
      Business Impact: HIGH
      Impact: Business Phone number is missing
      Recommendation: Populate Business Phone with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   28. MOBILE PHONE:
      Type: mobile_phone
      Status: BLANK
      Business Impact: HIGH
      Impact: Mobile Phone number is missing
      Recommendation: Populate Mobile Phone with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

   29. SECONDARY BENEFICIARY:
      Type: secondary_beneficiary
      Status: BLANK
      Business Impact: HIGH
      Impact: Secondary Beneficiary information is missing
      Recommendation: Populate Secondary Beneficiary with appropriate value
      Customer Impact: May affect document usability and compliance
      Page Number: N/A

💡 RECOMMENDATIONS:
   1. Fix 3 static text differences
   2. URGENT: Populate 8 critical blank fields
   3. Address regulatory compliance risks immediately

============================================================
🎯 NEXT STEPS:
1. Address critical blank fields immediately
2. Fix static text differences
3. Validate regulatory compliance
4. Re-run validation after fixes
============================================================